{"name": "bestselling-author-website", "version": "1.0.0", "description": "An ultra-modern website for a bestselling author, using Node.js, GSAP, and Barba.js.", "main": "server.js", "scripts": {"start": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["author", "book", "website", "gsap", "barba.js", "node.js"], "author": "", "license": "ISC", "dependencies": {"ejs": "^3.1.9", "express": "^4.18.2"}}