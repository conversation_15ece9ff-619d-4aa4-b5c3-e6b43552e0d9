function splitTextIntoSpans(selector) {
    const element = document.querySelector(selector);
    if (element) {
        const text = element.innerText;
        element.innerHTML = text.split('').map(char => `<span>${char === ' ' ? '&nbsp;' : char}</span>`).join('');
        return element.querySelectorAll('span');
    }
    return [];
}

function animateHomePage() {
    const chars = splitTextIntoSpans('.hero__title');
    const tl = gsap.timeline();

    tl.from(chars, {
        duration: 1.5,
        y: '100%',
        opacity: 0,
        stagger: 0.05,
        ease: 'power4.out'
    })
    .from('.hero__subtitle', { duration: 1, y: 20, opacity: 0, ease: 'power4.out' }, "-=1.2")
    .from('.btn', { duration: 1, y: 20, opacity: 0, ease: 'power4.out' }, "-=1")
    .to('.hero__image', { duration: 1.5, scale: 1, opacity: 1, ease: 'power4.out' }, "-=1.5");
}

function animateAboutPage() {
    const tl = gsap.timeline();
    tl.from('.anim-title', { duration: 1, y: 50, opacity: 0, ease: 'power4.out' })
      .to('.about-section__image', { duration: 1.5, scale: 1, opacity: 1, ease: 'power4.out' }, 0)
      .from('.about-section__text .anim-line', { duration: 1.2, y: 30, opacity: 0, ease: 'power4.out' }, 0.3);
}

function animateBooksPage() {
    const tl = gsap.timeline();
    tl.from('.anim-title', { duration: 1, y: 50, opacity: 0, ease: 'power4.out' })
      .from('.page-subtitle.anim-line', { duration: 1, y: 30, opacity: 0, ease: 'power4.out' }, "-=0.8")
      .from('.book-item', {
          duration: 1,
          y: 50,
          opacity: 0,
          stagger: 0.2,
          ease: 'power4.out'
      }, "-=0.5");
}


function runPageAnimations() {
    const namespace = document.querySelector('[data-barba-namespace]').dataset.barbaNamespace;

    if (namespace === 'home') {
        animateHomePage();
    } else if (namespace === 'about') {
        animateAboutPage();
    } else if (namespace === 'books') {
        animateBooksPage();
    }
}

// Run animations on initial page load
document.addEventListener('DOMContentLoaded', () => {
    runPageAnimations();
});

// Run animations after a page transition
document.addEventListener('content-animated-in', () => {
    runPageAnimations();
});