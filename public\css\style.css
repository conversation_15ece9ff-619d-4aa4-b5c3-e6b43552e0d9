/* ------------------- */
/* VARIABLES & RESET   */
/* ------------------- */
:root {
    --black: #0a0a0a;
    --white: #fefefe;
    --gray: #888;
    --font-main: 'Inter', sans-serif;
    --ease: cubic-bezier(0.23, 1, 0.32, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.transition-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #ffffff; /* dark bg */
    z-index: 9999;
    transform: translateY(100%);
    display: none;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.transition-quote {
    font-size: 4rem;
    color: #2c2c2c;
    font-weight: 700;
    text-align: center;
    max-width: 80%;
    line-height: 1.2;
    opacity: 0;
    transform: translateY(0px);
}

html {
    font-size: 16px;
}

body {
    font-family: var(--font-main);
    background-color: var(--black);
    color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

a {
    color: var(--white);
    text-decoration: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* ------------------- */
/* LAYOUT & COMPONENTS */
/* ------------------- */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 2rem 4rem;
    z-index: 100;
}

.site-header__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1600px;
    margin: 0 auto;
}

.logo {
    font-weight: 700;
    font-size: 1.25rem;
}

.main-nav a {
    margin-left: 2rem;
    font-weight: 500;
    position: relative;
    padding-bottom: 0.25rem;
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--white);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.4s var(--ease);
}

.main-nav a:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

main {
    width: 100%;
    min-height: 100vh;
    padding: 0 4rem;
}

.site-footer {
    text-align: center;
    padding: 4rem;
    color: var(--gray);
    font-size: 0.9rem;
}

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--white);
    border-radius: 50px;
    font-weight: 500;
    transition: background-color 0.3s, color 0.3s;
}

.btn:hover {
    background-color: var(--white);
    color: var(--black);
}

/* Page-specific sections */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
}

.hero__content {
    max-width: 50%;
}

.hero__title {
    font-size: 5rem;
    font-weight: 700;
    line-height: 1.1;
}

.hero__subtitle {
    font-size: 1.25rem;
    color: var(--gray);
    margin: 1.5rem 0 2.5rem;
}

.hero__image {
    width: 400px;
    height: auto;
    object-fit: cover;
    transform: scale(1.2); /* Initial state for animation */
    opacity: 0;
}

.page-intro {
    padding-top: 10rem;
    text-align: center;
    max-width: 800px;
    margin: 0 auto 5rem;
}

.page-title { font-size: 4rem; font-weight: 700; }
.page-subtitle { font-size: 1.25rem; color: var(--gray); margin-top: 1rem; }

.book-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 4rem;
    max-width: 1400px;
    margin: 0 auto;
}


.book-item img { margin-bottom: 1rem; }
.book-item h3 { font-size: 1.5rem; font-weight: 500; }

.about-section {
    display: flex;
    align-items: center;
    gap: 5rem;
    min-height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
}
.about-section__image-container { width: 40%; overflow: hidden; }
.about-section__image { width: 100%; opacity: 0; transform: scale(1.2); }
.about-section__text { width: 60%; }
.about-section__text p { font-size: 1.2rem; line-height: 1.7; color: var(--gray); }

/* ------------------- */
/* ANIMATION & TRANSITION */
/* ------------------- */
/* .transition-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: var(--black);
    transform: translateY(100%);
    z-index: 1000;
} */

/* Hiding elements for entry animation */
.anim-line, .anim-title {
    overflow: hidden;
}
/* .anim-line > *, .anim-title > *, .anim-chars > * {
    transform: translateY(100%);
    opacity: 0;
} */