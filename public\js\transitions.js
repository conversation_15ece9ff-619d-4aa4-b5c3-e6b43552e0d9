document.addEventListener('DOMContentLoaded', () => {
    function pageTransition() {
        return new Promise((resolve) => {
            const quote = document.querySelector('.transition-quote');
            const overlay = document.querySelector('.transition-overlay');

            const quotes = [
                "Design is intelligence made visible.",
                "Good design is good business.",
                "Simplicity is the ultimate sophistication.",
                "Less, but better."
            ];

            quote.textContent = quotes[Math.floor(Math.random() * quotes.length)];

            // Reset state
            gsap.set(overlay, { y: '100%', display: 'flex' });
            gsap.set(quote, { opacity: 0, y: 100 });

            const tl = gsap.timeline({
                defaults: { ease: 'power4.inOut' },
                onComplete: () => resolve() // let <PERSON><PERSON> enter after this
            });

            tl.to(overlay, { y: '0%', duration: 0.8 }, 0)
              .to(quote, { opacity: 1, y: 0, duration: 0.8 }, 0.2)
              .to(quote, { opacity: 0, y: -100, duration: 0.5 }, "+=0.8")
              .to(overlay, { y: '-100%', duration: 0.8 }, "-=0.3");
        });
    }

    barba.init({
        sync: true,
        transitions: [{
            async leave(data) {
                await gsap.to(data.current.container, { opacity: 0, duration: 0.3 });
            },
            async beforeEnter() {
                await pageTransition();
            },
            enter(data) {
                // No flicker fade-in
                document.dispatchEvent(new CustomEvent('content-animated-in'));
            }
        }]
    });
});
